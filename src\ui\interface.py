"""
Main CLI Interface - Interactive terminal interface with real-time updates
"""
import asyncio
import sys
from typing import Optional
from rich.console import Console
from rich.prompt import Prompt
from rich.text import Text
from rich.panel import Panel
from rich.live import Live
from prompt_toolkit import PromptSession
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory

from ..core.agent import ArienAgent
from ..config.settings import config_manager, ArienConfig
from .animations import (
    BallAnimation, ProgressIndicator, StreamingDisplay, 
    WelcomeScreen, console
)


class ArienCLI:
    """Main CLI interface for Arien AI"""
    
    def __init__(self):
        self.console = console
        self.agent: Optional[ArienAgent] = None
        self.config: Optional[ArienConfig] = None
        self.welcome_screen = WelcomeScreen(self.console)
        self.progress = ProgressIndicator(self.console)
        self.streaming = StreamingDisplay(self.console)
        self.ball_animation = BallAnimation(self.console)
        
        # Prompt session for better input handling
        self.prompt_session = PromptSession(
            history=InMemoryHistory(),
            auto_suggest=AutoSuggestFromHistory(),
        )
    
    async def start(self):
        """Start the CLI interface"""
        try:
            # Load or setup configuration
            await self._initialize_config()

            if not self.config:
                self.console.print("❌ Failed to initialize configuration. Exiting.")
                return

            # Initialize agent
            self.agent = ArienAgent(self.config)

            # Show welcome screen
            self.welcome_screen.show_welcome()

            # Start interactive loop
            await self._interactive_loop()

        except KeyboardInterrupt:
            self.console.print("\n👋 Goodbye!")
        except Exception as e:
            self.console.print(f"❌ Fatal error: {e}")

    async def _initialize_config(self):
        """Initialize configuration"""
        try:
            # Check if already configured
            if config_manager.is_configured():
                self.config = config_manager.load_config()
                return

            # Show setup required screen
            self.welcome_screen.show_setup_required()

            # Run interactive setup
            self.config = config_manager.setup_interactive()

        except Exception as e:
            self.console.print(f"❌ Configuration error: {e}")
            self.config = None

    async def _interactive_loop(self):
        """Main interactive loop"""
        self.console.print("[dim]Type 'help' for commands, 'quit' to exit[/dim]\n")

        while True:
            try:
                # Get user input
                user_input = await self._get_user_input()

                if not user_input.strip():
                    continue

                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                    continue
                elif user_input.lower() == 'clear':
                    self.agent.clear_conversation()
                    self.console.clear()
                    self.console.print("🧹 Conversation cleared!")
                    continue
                elif user_input.lower() == 'config':
                    await self._show_config()
                    continue
                elif user_input.lower() == 'status':
                    self._show_status()
                    continue

                # Process with AI agent
                await self._process_user_message(user_input)

            except KeyboardInterrupt:
                self.console.print("\n[dim]Use 'quit' to exit[/dim]")
            except Exception as e:
                self.progress.show_status(f"Error: {e}", "error")

    async def _get_user_input(self) -> str:
        """Get user input with prompt"""
        try:
            # Use prompt_toolkit for better input handling
            user_input = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.prompt_session.prompt("🤖 You: ")
            )
            return user_input
        except (EOFError, KeyboardInterrupt):
            return "quit"

    async def _process_user_message(self, message: str):
        """Process user message with AI agent"""
        try:
            # Show thinking animation
            thinking_task = asyncio.create_task(
                self.ball_animation.animate_with_text("Thinking...")
            )

            # Get AI response
            response = await self.agent.process_message(message)

            # Stop thinking animation
            self.ball_animation.stop()
            thinking_task.cancel()
            try:
                await thinking_task
            except asyncio.CancelledError:
                pass

            # Show response
            if response.success:
                self.streaming.show_final_text(response.content)

                # Show tool execution summary if tools were used
                if response.tool_calls:
                    self._show_tool_summary(response.tool_calls, response.tool_results)
            else:
                self.progress.show_status(f"Error: {response.error}", "error")

            # Show execution time
            if response.execution_time > 1:
                self.console.print(f"[dim]⏱️  Completed in {response.execution_time:.2f}s[/dim]")

        except Exception as e:
            # Stop any running animations
            self.ball_animation.stop()
            self.progress.show_status(f"Processing error: {e}", "error")

    def _show_tool_summary(self, tool_calls: list, tool_results: list):
        """Show summary of tool execution"""
        if not tool_calls:
            return

        summary = f"🔧 Executed {len(tool_calls)} tool(s):\n"

        for i, (call, result) in enumerate(zip(tool_calls, tool_results), 1):
            status = "✅" if result.success else "❌"
            summary += f"  {i}. {status} {call.name}"
            if result.execution_time > 0.1:
                summary += f" ({result.execution_time:.2f}s)"
            summary += "\n"

        self.progress.show_panel(summary, "Tool Execution", "green" if all(r.success for r in tool_results) else "yellow")

    def _show_help(self):
        """Show help information"""
        help_text = """
[bold cyan]🤖 Arien AI Commands[/bold cyan]

[yellow]Chat Commands:[/yellow]
• Just type your request naturally
• Ask me to run commands, search files, edit code, etc.

[yellow]Special Commands:[/yellow]
• [green]help[/green]     - Show this help
• [green]clear[/green]    - Clear conversation history
• [green]config[/green]   - Show configuration
• [green]status[/green]   - Show system status
• [green]quit[/green]     - Exit Arien AI

[yellow]Available Tools:[/yellow]
• [green]bash[/green]  - Execute shell commands
• [green]grep[/green]  - Search file contents
• [green]glob[/green]  - Find files by pattern
• [green]write[/green] - Create/update files
• [green]edit[/green]  - Modify existing files
• [green]web[/green]   - Search the internet

[blue]Examples:[/blue]
• "Find all Python files in this directory"
• "Search for TODO comments in my code"
• "Create a simple web server script"
• "Check system memory usage"
        """

        self.progress.show_panel(help_text, "Help", "blue")

    async def _show_config(self):
        """Show current configuration"""
        config_text = f"""
[yellow]LLM Provider:[/yellow] {self.config.llm.provider}
[yellow]Model:[/yellow] {self.config.llm.model}
[yellow]Base URL:[/yellow] {self.config.llm.base_url or 'Default'}

[yellow]UI Settings:[/yellow]
• Animation Speed: {self.config.ui.animation_speed}
• Show Progress: {self.config.ui.show_progress}
• Color Scheme: {self.config.ui.color_scheme}

[yellow]Tool Settings:[/yellow]
• Parallel Execution: {self.config.tools.parallel_execution}
• File Backup: {self.config.tools.file_backup}
• Bash Timeout: {self.config.tools.bash_timeout}s

[dim]Config file: {config_manager.config_file}[/dim]
        """

        self.progress.show_panel(config_text, "Configuration", "cyan")

    def _show_status(self):
        """Show system status"""
        import psutil
        import platform

        # Get system info
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        status_text = f"""
[yellow]System Information:[/yellow]
• OS: {platform.system()} {platform.release()}
• Python: {platform.python_version()}
• CPU Usage: {cpu_percent}%
• Memory: {memory.percent}% used ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)
• Disk: {disk.percent}% used ({disk.used // (1024**3):.1f}GB / {disk.total // (1024**3):.1f}GB)

[yellow]Agent Status:[/yellow]
• Provider: {self.config.llm.provider}
• Model: {self.config.llm.model}
• Conversation: {len(self.agent.conversation_history)} messages
• Tools Available: {len(self.agent.tool_executor.tools)}
        """

        self.progress.show_panel(status_text, "System Status", "green")


# Global CLI instance
cli = ArienCLI()

