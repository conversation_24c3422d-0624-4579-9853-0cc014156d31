#!/usr/bin/env python3
"""
Comprehensive test suite for Arien AI tools
"""
import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_bash_tool():
    """Test bash tool functionality"""
    print("🔧 Testing bash tool...")
    try:
        from src.tools.bash_tool import bash
        
        # Test simple command
        result = await bash('echo "Hello World"')
        assert result["success"], f"Bash tool failed: {result.get('output', 'Unknown error')}"
        print("  ✅ Simple echo command works")
        
        # Test directory listing
        result = await bash('dir' if os.name == 'nt' else 'ls')
        assert result["success"], f"Directory listing failed: {result.get('output', 'Unknown error')}"
        print("  ✅ Directory listing works")
        
        return True
    except Exception as e:
        print(f"  ❌ Bash tool error: {e}")
        return False

async def test_glob_tool():
    """Test glob tool functionality"""
    print("🔧 Testing glob tool...")
    try:
        from src.tools.glob_tool import glob
        
        # Test finding Python files
        result = await glob('*.py')
        assert result["success"], f"Glob tool failed: {result.get('output', 'Unknown error')}"
        print("  ✅ Python file search works")
        
        # Test recursive search
        result = await glob('*.py', recursive=True)
        assert result["success"], f"Recursive glob failed: {result.get('output', 'Unknown error')}"
        print("  ✅ Recursive search works")
        
        return True
    except Exception as e:
        print(f"  ❌ Glob tool error: {e}")
        return False

async def test_write_tool():
    """Test write tool functionality"""
    print("🔧 Testing write tool...")
    try:
        from src.tools.write_tool import write
        
        # Test file creation
        test_content = "Hello World\nThis is a test file."
        result = await write('test_file.txt', test_content)
        assert result["success"], f"Write tool failed: {result.get('output', 'Unknown error')}"
        print("  ✅ File creation works")
        
        # Verify file exists
        assert os.path.exists('test_file.txt'), "Test file was not created"
        print("  ✅ File exists after creation")
        
        return True
    except Exception as e:
        print(f"  ❌ Write tool error: {e}")
        return False

async def test_grep_tool():
    """Test grep tool functionality"""
    print("🔧 Testing grep tool...")
    try:
        from src.tools.grep_tool import grep
        
        # Test searching in the test file
        result = await grep('Hello', 'test_file.txt')
        assert result["success"], f"Grep tool failed: {result.get('output', 'Unknown error')}"
        print("  ✅ File content search works")
        
        # Test pattern search
        result = await grep('test', '*.txt')
        assert result["success"], f"Pattern search failed: {result.get('output', 'Unknown error')}"
        print("  ✅ Pattern search works")
        
        return True
    except Exception as e:
        print(f"  ❌ Grep tool error: {e}")
        return False

async def test_edit_tool():
    """Test edit tool functionality"""
    print("🔧 Testing edit tool...")
    try:
        from src.tools.edit_tool import edit
        
        # Test text replacement
        result = await edit('test_file.txt', 'Hello', 'Hi')
        assert result["success"], f"Edit tool failed: {result.get('output', 'Unknown error')}"
        print("  ✅ Text replacement works")
        
        return True
    except Exception as e:
        print(f"  ❌ Edit tool error: {e}")
        return False

async def test_web_tool():
    """Test web tool functionality"""
    print("🔧 Testing web tool...")
    try:
        from src.tools.web_tool import web
        
        # Test simple search
        result = await web('Python programming', max_results=2)
        assert result["success"], f"Web tool failed: {result.get('output', 'Unknown error')}"
        print("  ✅ Web search works")
        
        return True
    except Exception as e:
        print(f"  ❌ Web tool error: {e}")
        return False

async def test_tool_executor():
    """Test tool executor functionality"""
    print("🔧 Testing tool executor...")
    try:
        from src.core.tool_executor import ToolExecutor, ToolCall
        
        executor = ToolExecutor()
        
        # Test single tool execution
        tool_call = ToolCall(
            name="bash",
            arguments={"command": "echo 'Tool executor test'"},
            call_id="test_1"
        )
        
        result = await executor.execute_tool(tool_call)
        assert result.success, f"Tool executor failed: {result.error}"
        print("  ✅ Single tool execution works")
        
        return True
    except Exception as e:
        print(f"  ❌ Tool executor error: {e}")
        return False

async def test_config_system():
    """Test configuration system"""
    print("🔧 Testing configuration system...")
    try:
        from src.config.settings import ConfigManager
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        assert config is not None, "Failed to load configuration"
        assert hasattr(config, 'llm'), "Configuration missing LLM settings"
        assert hasattr(config, 'ui'), "Configuration missing UI settings"
        assert hasattr(config, 'tools'), "Configuration missing tool settings"
        
        print("  ✅ Configuration loading works")
        print(f"  ✅ LLM Provider: {config.llm.provider}")
        print(f"  ✅ Model: {config.llm.model}")
        
        return True
    except Exception as e:
        print(f"  ❌ Configuration error: {e}")
        return False

async def cleanup():
    """Clean up test files"""
    try:
        if os.path.exists('test_file.txt'):
            os.remove('test_file.txt')
        print("🧹 Cleanup completed")
    except Exception as e:
        print(f"⚠️  Cleanup warning: {e}")

async def main():
    """Run all tests"""
    print("🚀 Starting Arien AI Tool Test Suite")
    print("=" * 50)
    
    tests = [
        test_config_system,
        test_bash_tool,
        test_glob_tool,
        test_write_tool,
        test_grep_tool,
        test_edit_tool,
        test_web_tool,
        test_tool_executor,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if await test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    await cleanup()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Arien AI is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
