#!/usr/bin/env python3
"""
Simple test to identify issues
"""
import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        from src.config.settings import ConfigManager
        print("✅ Config import successful")
    except Exception as e:
        print(f"❌ Config import failed: {e}")
        return False
    
    try:
        from src.core.llm_providers import create_llm_provider
        print("✅ LLM provider import successful")
    except Exception as e:
        print(f"❌ LLM provider import failed: {e}")
        return False
    
    try:
        from src.core.tool_executor import ToolExecutor
        print("✅ Tool executor import successful")
    except Exception as e:
        print(f"❌ Tool executor import failed: {e}")
        return False
    
    try:
        from src.tools.bash_tool import bash
        print("✅ Bash tool import successful")
    except Exception as e:
        print(f"❌ Bash tool import failed: {e}")
        return False
    
    try:
        from src.tools.glob_tool import glob
        print("✅ Glob tool import successful")
    except Exception as e:
        print(f"❌ Glob tool import failed: {e}")
        return False
    
    try:
        from src.tools.write_tool import write
        print("✅ Write tool import successful")
    except Exception as e:
        print(f"❌ Write tool import failed: {e}")
        return False
    
    try:
        from src.tools.grep_tool import grep
        print("✅ Grep tool import successful")
    except Exception as e:
        print(f"❌ Grep tool import failed: {e}")
        return False
    
    try:
        from src.tools.edit_tool import edit
        print("✅ Edit tool import successful")
    except Exception as e:
        print(f"❌ Edit tool import failed: {e}")
        return False
    
    try:
        from src.tools.web_tool import web
        print("✅ Web tool import successful")
    except Exception as e:
        print(f"❌ Web tool import failed: {e}")
        return False
    
    try:
        from src.ui.interface import ArienCLI
        print("✅ UI interface import successful")
    except Exception as e:
        print(f"❌ UI interface import failed: {e}")
        return False
    
    return True

async def test_simple_tool():
    """Test a simple tool execution"""
    print("\nTesting simple tool execution...")
    
    try:
        from src.tools.bash_tool import bash
        
        print("Executing simple bash command...")
        result = await bash('echo "Hello from test"')
        
        if result["success"]:
            print("✅ Bash tool execution successful")
            print(f"Output: {result['output'][:100]}...")
            return True
        else:
            print(f"❌ Bash tool execution failed: {result.get('output', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Tool execution error: {e}")
        return False

def test_config():
    """Test configuration"""
    print("\nTesting configuration...")
    
    try:
        from src.config.settings import ConfigManager
        
        cm = ConfigManager()
        config = cm.load_config()
        
        print(f"✅ Config loaded: {config.llm.provider}")
        print(f"✅ Model: {config.llm.model}")
        print(f"✅ API key configured: {'Yes' if config.llm.api_key else 'No'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

async def main():
    """Run simple tests"""
    print("🔍 Running Simple Diagnostic Tests")
    print("=" * 40)
    
    # Test imports
    if not test_imports():
        print("❌ Import tests failed")
        return 1
    
    # Test config
    if not test_config():
        print("❌ Config tests failed")
        return 1
    
    # Test simple tool
    if not await test_simple_tool():
        print("❌ Tool tests failed")
        return 1
    
    print("\n🎉 All simple tests passed!")
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ Test runner error: {e}")
        sys.exit(1)
