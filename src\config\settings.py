"""
Configuration management for Arien AI CLI
"""
import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class LLMConfig:
    """LLM Provider configuration"""
    provider: str  # 'deepseek' or 'ollama'
    api_key: Optional[str] = None
    model: str = "deepseek-chat"
    base_url: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3


@dataclass
class UIConfig:
    """UI configuration"""
    animation_speed: float = 0.1
    show_progress: bool = True
    color_scheme: str = "default"
    max_output_lines: int = 1000


@dataclass
class ToolConfig:
    """Tool execution configuration"""
    bash_timeout: int = 300
    web_search_results: int = 5
    file_backup: bool = True
    parallel_execution: bool = True


@dataclass
class ArienConfig:
    """Main configuration class"""
    llm: LLMConfig
    ui: UIConfig
    tools: ToolConfig
    debug: bool = False
    log_level: str = "INFO"


class ConfigManager:
    """Manages configuration loading, saving, and validation"""

    def __init__(self):
        self.config_dir = Path.home() / ".arien-ai"
        self.config_file = self.config_dir / "config.yaml"
        self.config_dir.mkdir(exist_ok=True)

    def load_config(self) -> ArienConfig:
        """Load configuration from file or create default"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    data = yaml.safe_load(f)
                return self._dict_to_config(data)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self._create_default_config()
        else:
            return self._create_default_config()

    def save_config(self, config: ArienConfig) -> bool:
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                yaml.dump(asdict(config), f, default_flow_style=False)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False

    def _create_default_config(self) -> ArienConfig:
        """Create default configuration"""
        return ArienConfig(
            llm=LLMConfig(
                provider="deepseek",
                model="deepseek-chat"
            ),
            ui=UIConfig(),
            tools=ToolConfig()
        )

    def _dict_to_config(self, data: Dict[str, Any]) -> ArienConfig:
        """Convert dictionary to configuration object"""
        # Ensure LLM config has required fields
        llm_data = data.get('llm', {})
        if 'provider' not in llm_data:
            llm_data['provider'] = 'deepseek'

        # Filter UI config to only include valid fields
        ui_data = data.get('ui', {})
        valid_ui_fields = {'animation_speed', 'show_progress', 'color_scheme', 'max_output_lines'}
        ui_data = {k: v for k, v in ui_data.items() if k in valid_ui_fields}

        # Filter tool config to only include valid fields
        tool_data = data.get('tools', {})
        valid_tool_fields = {'bash_timeout', 'web_search_results', 'file_backup', 'parallel_execution'}
        tool_data = {k: v for k, v in tool_data.items() if k in valid_tool_fields}

        return ArienConfig(
            llm=LLMConfig(**llm_data),
            ui=UIConfig(**ui_data),
            tools=ToolConfig(**tool_data),
            debug=data.get('debug', False),
            log_level=data.get('log_level', 'INFO')
        )

    def is_configured(self) -> bool:
        """Check if the system is properly configured"""
        config = self.load_config()

        # Check if LLM provider is configured
        if config.llm.provider == "deepseek":
            return config.llm.api_key is not None and config.llm.api_key.strip() != ""
        elif config.llm.provider == "ollama":
            return config.llm.base_url is not None

        return False

    def setup_interactive(self) -> ArienConfig:
        """Interactive setup wizard"""
        from rich.console import Console
        from rich.prompt import Prompt, Confirm
        from rich.panel import Panel

        console = Console()

        console.print(Panel(
            "[bold cyan]🤖 Arien AI Setup Wizard[/bold cyan]\n\n"
            "Let's configure your AI assistant!",
            title="Welcome",
            border_style="cyan"
        ))

        # Choose provider
        console.print("\n[yellow]Choose your LLM provider:[/yellow]")
        console.print("1. [green]Deepseek[/green] - Cloud-based AI (requires API key)")
        console.print("2. [green]Ollama[/green] - Local AI models")

        while True:
            choice = Prompt.ask("Select provider", choices=["1", "2"], default="1")
            if choice in ["1", "2"]:
                break

        if choice == "1":
            # Deepseek setup
            console.print("\n[blue]Setting up Deepseek...[/blue]")
            api_key = Prompt.ask("Enter your Deepseek API key", password=True)

            console.print("\n[yellow]Choose model:[/yellow]")
            console.print("1. [green]deepseek-chat[/green] - General purpose")
            console.print("2. [green]deepseek-reasoner[/green] - Advanced reasoning")

            model_choice = Prompt.ask("Select model", choices=["1", "2"], default="1")
            model = "deepseek-chat" if model_choice == "1" else "deepseek-reasoner"

            llm_config = LLMConfig(
                provider="deepseek",
                api_key=api_key,
                model=model,
                base_url="https://api.deepseek.com"
            )
        else:
            # Ollama setup
            console.print("\n[blue]Setting up Ollama...[/blue]")
            base_url = Prompt.ask("Ollama base URL", default="http://localhost:11434")
            model = Prompt.ask("Model name", default="llama2")

            llm_config = LLMConfig(
                provider="ollama",
                model=model,
                base_url=base_url
            )

        # UI preferences
        console.print("\n[yellow]UI Preferences:[/yellow]")
        show_progress = Confirm.ask("Show progress animations?", default=True)
        animation_speed = float(Prompt.ask("Animation speed (0.05-0.5)", default="0.1"))

        ui_config = UIConfig(
            show_progress=show_progress,
            animation_speed=animation_speed
        )

        # Tool preferences
        console.print("\n[yellow]Tool Settings:[/yellow]")
        parallel_execution = Confirm.ask("Enable parallel tool execution?", default=True)
        file_backup = Confirm.ask("Create backups when editing files?", default=True)

        tools_config = ToolConfig(
            parallel_execution=parallel_execution,
            file_backup=file_backup
        )

        # Create final config
        config = ArienConfig(
            llm=llm_config,
            ui=ui_config,
            tools=tools_config
        )

        # Save configuration
        if self.save_config(config):
            console.print("\n✅ [green]Configuration saved successfully![/green]")
        else:
            console.print("\n❌ [red]Failed to save configuration[/red]")

        return config


# Global config manager instance
config_manager = ConfigManager()
