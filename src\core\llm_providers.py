"""
LLM Provider implementations for Deepseek and Ollama
"""
import json
import asyncio
import aiohttp
import requests
from typing import Dict, List, Any, Optional, AsyncGenerator
from abc import ABC, abstractmethod

from ..config.settings import LLMConfig
from ..config.prompts import SYSTEM_PROMPT, TOOL_DESCRIPTIONS


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, config: LLMConfig):
        self.config = config
    
    @abstractmethod
    async def chat_completion(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Generate chat completion"""
        pass
    
    @abstractmethod
    async def stream_completion(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """Stream chat completion"""
        pass


class DeepseekProvider(LLMProvider):
    """Deepseek API provider"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.deepseek.com"
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
    
    def _prepare_tools(self, tools: Optional[List[Dict]] = None) -> List[Dict]:
        """Prepare tools for Deepseek API format"""
        if not tools:
            return []

        formatted_tools = []
        for tool_name, tool_info in TOOL_DESCRIPTIONS.items():
            formatted_tools.append({
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tool_info["description"],
                    "parameters": {
                        "type": "object",
                        "properties": tool_info["parameters"],
                        "required": tool_info.get("required", list(tool_info["parameters"].keys()))
                    }
                }
            })
        return formatted_tools
    
    async def chat_completion(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Generate chat completion with Deepseek"""
        url = f"{self.base_url}/chat/completions"
        
        # Add system prompt if not present
        if not messages or messages[0]["role"] != "system":
            messages.insert(0, {"role": "system", "content": SYSTEM_PROMPT})
        
        payload = {
            "model": self.config.model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4000,
            "stream": False
        }
        
        # Add tools if provided
        if tools:
            payload["tools"] = self._prepare_tools(tools)
            payload["tool_choice"] = "auto"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url, 
                headers=self.headers, 
                json=payload,
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Deepseek API error {response.status}: {error_text}")
    
    async def stream_completion(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """Stream chat completion with Deepseek"""
        url = f"{self.base_url}/chat/completions"

        # Add system prompt if not present
        if not messages or messages[0]["role"] != "system":
            messages.insert(0, {"role": "system", "content": SYSTEM_PROMPT})

        payload = {
            "model": self.config.model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4000,
            "stream": True
        }

        # Add tools if provided
        if tools:
            payload["tools"] = self._prepare_tools(tools)
            payload["tool_choice"] = "auto"

        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                headers=self.headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Deepseek API error {response.status}: {error_text}")

                tool_calls_buffer = {}

                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line.startswith('data: '):
                        data = line[6:]
                        if data == '[DONE]':
                            break
                        try:
                            chunk = json.loads(data)
                            if 'choices' in chunk and chunk['choices']:
                                delta = chunk['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    yield delta['content']
                                elif 'tool_calls' in delta:
                                    # Handle tool calls streaming
                                    for tool_call in delta['tool_calls']:
                                        call_id = tool_call.get('id', 'default')

                                        if call_id not in tool_calls_buffer:
                                            tool_calls_buffer[call_id] = {
                                                'id': call_id,
                                                'type': tool_call.get('type', 'function'),
                                                'function': {
                                                    'name': '',
                                                    'arguments': ''
                                                }
                                            }

                                        # Accumulate function data
                                        if 'function' in tool_call:
                                            func_data = tool_call['function']
                                            if 'name' in func_data:
                                                tool_calls_buffer[call_id]['function']['name'] += func_data['name']
                                            if 'arguments' in func_data:
                                                tool_calls_buffer[call_id]['function']['arguments'] += func_data['arguments']
                        except json.JSONDecodeError:
                            continue

                # Yield completed tool calls at the end
                if tool_calls_buffer:
                    for call_id, tool_call in tool_calls_buffer.items():
                        yield f"\n__TOOL_CALL__{json.dumps(tool_call)}__TOOL_CALL__\n"


class OllamaProvider(LLMProvider):
    """Ollama local provider"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.base_url = config.base_url or "http://localhost:11434"
    
    async def chat_completion(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Generate chat completion with Ollama"""
        url = f"{self.base_url}/api/chat"
        
        # Add system prompt if not present
        if not messages or messages[0]["role"] != "system":
            messages.insert(0, {"role": "system", "content": SYSTEM_PROMPT})
        
        payload = {
            "model": self.config.model,
            "messages": messages,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "num_predict": 4000
            }
        }
        
        # Add tools information to system message if provided
        if tools:
            tool_info = "\n\nAvailable Tools:\n"
            for tool_name, tool_desc in TOOL_DESCRIPTIONS.items():
                tool_info += f"- {tool_name}: {tool_desc['description']}\n"
            messages[0]["content"] += tool_info
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url, 
                json=payload,
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Ollama API error {response.status}: {error_text}")
    
    async def stream_completion(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """Stream chat completion with Ollama"""
        url = f"{self.base_url}/api/chat"
        
        # Add system prompt if not present
        if not messages or messages[0]["role"] != "system":
            messages.insert(0, {"role": "system", "content": SYSTEM_PROMPT})
        
        payload = {
            "model": self.config.model,
            "messages": messages,
            "stream": True,
            "options": {
                "temperature": 0.7,
                "num_predict": 4000
            }
        }
        
        # Add tools information to system message if provided
        if tools:
            tool_info = "\n\nAvailable Tools:\n"
            for tool_name, tool_desc in TOOL_DESCRIPTIONS.items():
                tool_info += f"- {tool_name}: {tool_desc['description']}\n"
            messages[0]["content"] += tool_info
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url, 
                json=payload,
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Ollama API error {response.status}: {error_text}")
                
                async for line in response.content:
                    try:
                        chunk = json.loads(line.decode('utf-8'))
                        if 'message' in chunk and 'content' in chunk['message']:
                            yield chunk['message']['content']
                        if chunk.get('done', False):
                            break
                    except json.JSONDecodeError:
                        continue


def create_llm_provider(config: LLMConfig) -> LLMProvider:
    """Factory function to create LLM provider"""
    if config.provider == "deepseek":
        return DeepseekProvider(config)
    elif config.provider == "ollama":
        return OllamaProvider(config)
    else:
        raise ValueError(f"Unknown provider: {config.provider}")
