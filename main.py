#!/usr/bin/env python3
"""
Arien AI - Advanced Terminal Assistant
Main entry point for the CLI application
"""
import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ui.interface import <PERSON>enCL<PERSON>
from src.utils.error_handler import setup_global_exception_handler
from src.utils.logger import setup_logger


def main():
    """Main entry point"""
    # Setup global error handling
    setup_global_exception_handler()
    
    # Setup logging
    logger = setup_logger(
        level="INFO",
        log_file=Path.home() / ".arien-ai" / "logs" / "arien.log"
    )
    
    try:
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 or higher is required")
            sys.exit(1)
        
        # Run the CLI
        cli = ArienCLI()
        asyncio.run(cli.start())
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        logger.critical(f"Fatal error: {e}")
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
