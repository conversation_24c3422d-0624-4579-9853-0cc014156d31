"""
Main AI Agent - Core intelligence with function calling capabilities
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass

from .llm_providers import create_llm_provider, LLMProvider
from .tool_executor import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>all, <PERSON>lResult, ExecutionMode
from .retry_logic import <PERSON><PERSON><PERSON>og<PERSON>, NETWORK_RETRY
from ..config.settings import ArienConfig
from ..config.prompts import SYSTEM_PROMPT, TOOL_DESCRIPTIONS


@dataclass
class AgentResponse:
    """Response from the AI agent"""
    content: str
    tool_calls: List[ToolCall]
    tool_results: List[ToolResult]
    execution_time: float
    success: bool
    error: Optional[str] = None


class ArienAgent:
    """Main AI agent with function calling capabilities"""

    def __init__(self, config: ArienConfig):
        self.config = config
        self.llm_provider: Optional[LLMProvider] = None
        self.tool_executor = ToolExecutor()
        self.retry_logic = RetryLogic(NETWORK_RETRY)
        self.conversation_history: List[Dict[str, str]] = []

        # Initialize LLM provider
        self._initialize_provider()

    def _initialize_provider(self):
        """Initialize the LLM provider"""
        try:
            self.llm_provider = create_llm_provider(self.config.llm)
        except Exception as e:
            print(f"Failed to initialize LLM provider: {e}")
            self.llm_provider = None

    async def process_message(self, user_message: str, stream: bool = True) -> AgentResponse:
        """
        Process user message and execute any required tools

        Args:
            user_message: User's input message
            stream: Whether to stream the response

        Returns:
            AgentResponse with content and tool results
        """
        start_time = time.time()

        if not self.llm_provider:
            return AgentResponse(
                content="❌ LLM provider not configured. Please run setup first.",
                tool_calls=[],
                tool_results=[],
                execution_time=0,
                success=False,
                error="LLM provider not configured"
            )

        try:
            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message
            })

            # Get AI response
            if stream:
                response_content = await self._stream_ai_response()
            else:
                response_content = await self._get_ai_response()

            # Parse and execute tool calls
            tool_calls = self._extract_tool_calls(response_content)
            tool_results = []

            if tool_calls:
                tool_results = await self.tool_executor.execute_tools(
                    tool_calls, ExecutionMode.AUTO
                )

                # Add tool results to conversation and get follow-up response
                follow_up_content = await self._process_tool_results(
                    response_content, tool_results, stream
                )

                if follow_up_content:
                    response_content += "\n\n" + follow_up_content

            # Add assistant response to conversation
            self.conversation_history.append({
                "role": "assistant",
                "content": response_content
            })

            execution_time = time.time() - start_time

            return AgentResponse(
                content=response_content,
                tool_calls=tool_calls,
                tool_results=tool_results,
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return AgentResponse(
                content=f"❌ Error processing message: {str(e)}",
                tool_calls=[],
                tool_results=[],
                execution_time=execution_time,
                success=False,
                error=str(e)
            )

    async def _get_ai_response(self) -> str:
        """Get AI response without streaming"""
        try:
            response = await self.retry_logic.execute_with_retry(
                self.llm_provider.chat_completion,
                self.conversation_history,
                list(TOOL_DESCRIPTIONS.keys())
            )
            
            if "choices" in response and response["choices"]:
                choice = response["choices"][0]
                if "message" in choice:
                    return choice["message"].get("content", "")
                elif "text" in choice:
                    return choice["text"]
            elif "message" in response:
                return response["message"].get("content", "")
            
            return "No response generated"
            
        except Exception as e:
            raise Exception(f"AI response error: {str(e)}")
    
    async def _stream_ai_response(self) -> str:
        """Get AI response with streaming"""
        try:
            content = ""
            async for chunk in self.llm_provider.stream_completion(
                self.conversation_history,
                list(TOOL_DESCRIPTIONS.keys())
            ):
                content += chunk
                # Note: Actual streaming display is handled by the UI layer
            
            return content
            
        except Exception as e:
            raise Exception(f"AI streaming error: {str(e)}")
    
    def _extract_tool_calls(self, response_content: str) -> List[ToolCall]:
        """Extract tool calls from AI response"""
        import json
        import re

        tool_calls = []

        # First, try to extract tool calls from the special markers
        tool_call_pattern = r'__TOOL_CALL__(.*?)__TOOL_CALL__'
        matches = re.findall(tool_call_pattern, response_content, re.DOTALL)

        for match in matches:
            try:
                tool_call_data = json.loads(match.strip())
                if 'function' in tool_call_data:
                    func_data = tool_call_data['function']
                    name = func_data.get('name', '')
                    arguments_str = func_data.get('arguments', '{}')

                    # Parse arguments JSON
                    try:
                        arguments = json.loads(arguments_str) if arguments_str else {}
                    except json.JSONDecodeError:
                        arguments = {"command": arguments_str}  # Fallback for malformed JSON

                    tool_calls.append(ToolCall(
                        name=name,
                        arguments=arguments,
                        call_id=tool_call_data.get('id', f"call_{len(tool_calls)}")
                    ))
            except json.JSONDecodeError:
                continue

        # If no tool calls found with markers, fall back to text parsing
        if not tool_calls:
            tool_calls = self.tool_executor.parse_tool_calls_from_response(response_content)

        return tool_calls
    
    async def _process_tool_results(self, original_response: str, 
                                   tool_results: List[ToolResult], 
                                   stream: bool = True) -> str:
        """Process tool results and get AI follow-up"""
        if not tool_results:
            return ""
        
        # Create tool results summary for AI
        results_summary = "Tool Execution Results:\n\n"
        
        for result in tool_results:
            results_summary += f"Tool: {result.tool_name}\n"
            results_summary += f"Success: {result.success}\n"
            results_summary += f"Execution Time: {result.execution_time:.2f}s\n"
            
            if result.success:
                # Don't show full output to AI, just summary
                output_preview = result.output[:500] + "..." if len(result.output) > 500 else result.output
                results_summary += f"Output: {output_preview}\n"
            else:
                results_summary += f"Error: {result.error or 'Unknown error'}\n"
            
            results_summary += "\n"
        
        # Add tool results to conversation
        self.conversation_history.append({
            "role": "system",
            "content": f"Tool execution completed. {results_summary}"
        })
        
        # Get AI follow-up response
        try:
            if stream:
                follow_up = await self._stream_ai_response()
            else:
                follow_up = await self._get_ai_response()
            
            return follow_up
            
        except Exception as e:
            return f"Error processing tool results: {str(e)}"
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history.clear()
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary of current conversation"""
        return {
            "message_count": len(self.conversation_history),
            "last_message": self.conversation_history[-1] if self.conversation_history else None,
            "conversation_length": sum(len(msg["content"]) for msg in self.conversation_history)
        }
    
    async def stream_response(self, user_message: str) -> AsyncGenerator[str, None]:
        """Stream AI response in real-time"""
        if not self.llm_provider:
            yield "❌ LLM provider not configured. Please run setup first."
            return
        
        try:
            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message
            })
            
            # Stream AI response
            response_content = ""
            tool_call_content = ""

            async for chunk in self.llm_provider.stream_completion(
                self.conversation_history,
                list(TOOL_DESCRIPTIONS.keys())
            ):
                response_content += chunk

                # Check if this chunk contains tool call markers
                if "__TOOL_CALL__" in chunk:
                    tool_call_content += chunk
                    # Don't yield tool call markers to user
                    continue
                else:
                    yield chunk

            # Process tool calls after streaming completes
            tool_calls = self._extract_tool_calls(response_content)

            if tool_calls:
                yield "\n\n🔧 Executing tools...\n"

                tool_results = await self.tool_executor.execute_tools(
                    tool_calls, ExecutionMode.AUTO
                )

                # Show tool results
                for result in tool_results:
                    if result.success:
                        yield f"\n✅ {result.tool_name}: Completed in {result.execution_time:.2f}s\n"
                        yield f"{result.output}\n"
                    else:
                        yield f"\n❌ {result.tool_name}: Failed - {result.error}\n"
                
                # Get follow-up response
                follow_up_content = await self._process_tool_results(
                    response_content, tool_results, False
                )
                
                if follow_up_content:
                    yield "\n\n"
                    yield follow_up_content
                    response_content += "\n\n" + follow_up_content
            
            # Clean response content for conversation history (remove tool call markers)
            clean_content = response_content
            import re
            clean_content = re.sub(r'__TOOL_CALL__.*?__TOOL_CALL__', '', clean_content, flags=re.DOTALL)

            # Add final response to conversation
            self.conversation_history.append({
                "role": "assistant",
                "content": clean_content.strip()
            })
            
        except Exception as e:
            yield f"\n\n❌ Error: {str(e)}"
