"""
Tool Executor - Manages function tool execution with parallel/sequential support
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from ..tools.bash_tool import bash
from ..tools.grep_tool import grep
from ..tools.glob_tool import glob
from ..tools.write_tool import write
from ..tools.edit_tool import edit
from ..tools.web_tool import web
from .retry_logic import RetryLogic, NETWORK_RETRY


class ExecutionMode(Enum):
    """Tool execution modes"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    AUTO = "auto"


@dataclass
class ToolCall:
    """Represents a tool function call"""
    name: str
    arguments: Dict[str, Any]
    call_id: Optional[str] = None


@dataclass
class ToolResult:
    """Result of tool execution"""
    tool_name: str
    success: bool
    output: str
    execution_time: float
    raw_result: Dict[str, Any]
    error: Optional[str] = None
    call_id: Optional[str] = None


class ToolExecutor:
    """Manages execution of AI function tools"""
    
    def __init__(self):
        self.tools = {
            "bash": bash,
            "grep": grep,
            "glob": glob,
            "write": write,
            "edit": edit,
            "web": web
        }
        self.retry_logic = RetryLogic(NETWORK_RETRY)
    
    async def execute_tool(self, tool_call: ToolCall) -> ToolResult:
        """Execute a single tool call"""
        start_time = time.time()

        try:
            if tool_call.name not in self.tools:
                return ToolResult(
                    tool_name=tool_call.name,
                    success=False,
                    output=f"Unknown tool: {tool_call.name}",
                    execution_time=0,
                    raw_result={},
                    error=f"Tool '{tool_call.name}' not found",
                    call_id=tool_call.call_id
                )

            # Get tool function
            tool_func = self.tools[tool_call.name]

            # Execute tool with retry logic
            raw_result = await self.retry_logic.execute_with_retry(
                tool_func, **tool_call.arguments
            )

            execution_time = time.time() - start_time

            # Format output
            if isinstance(raw_result, dict):
                success = raw_result.get("success", True)
                output = self._format_tool_output(tool_call.name, raw_result)
                error = raw_result.get("error")
            else:
                success = True
                output = str(raw_result)
                error = None

            return ToolResult(
                tool_name=tool_call.name,
                success=success,
                output=output,
                execution_time=execution_time,
                raw_result=raw_result if isinstance(raw_result, dict) else {"result": raw_result},
                error=error,
                call_id=tool_call.call_id
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return ToolResult(
                tool_name=tool_call.name,
                success=False,
                output=f"Tool execution failed: {str(e)}",
                execution_time=execution_time,
                raw_result={},
                error=str(e),
                call_id=tool_call.call_id
            )

    async def execute_sequential(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """Execute tool calls sequentially"""
        results = []

        for tool_call in tool_calls:
            result = await self.execute_tool(tool_call)
            results.append(result)

            # Stop on critical errors
            if not result.success and self._is_critical_error(result.error):
                break

        return results

    async def execute_parallel(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """Execute tool calls in parallel"""
        # Group tools by dependency
        independent_calls, dependent_calls = self._analyze_dependencies(tool_calls)

        results = []

        # Execute independent tools in parallel
        if independent_calls:
            parallel_tasks = [
                self.execute_tool(call) for call in independent_calls
            ]
            parallel_results = await asyncio.gather(*parallel_tasks, return_exceptions=True)

            for i, result in enumerate(parallel_results):
                if isinstance(result, Exception):
                    # Convert exception to ToolResult
                    call = independent_calls[i]
                    results.append(ToolResult(
                        tool_name=call.name,
                        success=False,
                        output=f"Parallel execution failed: {str(result)}",
                        execution_time=0,
                        raw_result={},
                        error=str(result),
                        call_id=call.call_id
                    ))
                else:
                    results.append(result)

        # Execute dependent tools sequentially
        if dependent_calls:
            sequential_results = await self.execute_sequential(dependent_calls)
            results.extend(sequential_results)

        return results

    def _analyze_dependencies(self, tool_calls: List[ToolCall]) -> tuple[List[ToolCall], List[ToolCall]]:
        """Analyze tool dependencies for parallel execution"""
        # Simple heuristic: file operations are dependent, others are independent
        file_tools = {"write", "edit"}
        bash_tools = {"bash"}

        independent = []
        dependent = []

        has_file_ops = any(call.name in file_tools for call in tool_calls)
        has_bash_ops = any(call.name in bash_tools for call in tool_calls)

        for call in tool_calls:
            if call.name in file_tools and (has_bash_ops or len([c for c in tool_calls if c.name in file_tools]) > 1):
                # File operations with bash or multiple file ops should be sequential
                dependent.append(call)
            elif call.name in bash_tools and has_file_ops:
                # Bash operations with file ops should be sequential
                dependent.append(call)
            else:
                # Safe to run in parallel
                independent.append(call)

        return independent, dependent

    def _is_critical_error(self, error: Optional[str]) -> bool:
        """Check if error is critical and should stop execution"""
        if not error:
            return False

        critical_patterns = [
            "permission denied",
            "authentication failed",
            "file not found",
            "syntax error",
            "invalid arguments"
        ]

        error_lower = error.lower()
        return any(pattern in error_lower for pattern in critical_patterns)

    def _format_tool_output(self, tool_name: str, result: Dict[str, Any]) -> str:
        """Format tool output for display"""
        if not result.get("success", True):
            return f"❌ {tool_name} failed: {result.get('error', 'Unknown error')}"

        # Tool-specific formatting
        if tool_name == "bash":
            output = f"🔧 Command executed: {result.get('command', '')}\n"
            if result.get("stdout"):
                output += f"Output:\n{result['stdout']}"
            if result.get("stderr"):
                output += f"\nErrors:\n{result['stderr']}"
            return output

        elif tool_name == "grep":
            matches = result.get("matches", [])
            output = f"🔍 Found {len(matches)} matches"
            if matches:
                output += ":\n"
                for match in matches[:5]:  # Show first 5 matches
                    output += f"  {match.get('file', '')}: {match.get('line', '')}\n"
                if len(matches) > 5:
                    output += f"  ... and {len(matches) - 5} more matches"
            return output

        elif tool_name == "glob":
            files = result.get("files", [])
            output = f"📁 Found {len(files)} files"
            if files:
                output += ":\n"
                for file_info in files[:10]:  # Show first 10 files
                    output += f"  {file_info.get('path', '')}\n"
                if len(files) > 10:
                    output += f"  ... and {len(files) - 10} more files"
            return output

        elif tool_name == "write":
            return f"✍️ File written: {result.get('filepath', '')} ({result.get('size', 0)} chars)"

        elif tool_name == "edit":
            return f"✏️ File edited: {result.get('filepath', '')} ({result.get('replacements', 0)} changes)"

        elif tool_name == "web":
            results_count = len(result.get("results", []))
            return f"🌐 Web search completed: {results_count} results found"

        # Default formatting
        return f"✅ {tool_name} completed successfully"

    async def execute_tools(self, tool_calls: List[ToolCall], mode: ExecutionMode) -> List[ToolResult]:
        """Execute tools based on the specified mode"""
        if not tool_calls:
            return []

        if mode == ExecutionMode.PARALLEL:
            return await self.execute_parallel(tool_calls)
        elif mode == ExecutionMode.SEQUENTIAL:
            return await self.execute_sequential(tool_calls)
        elif mode == ExecutionMode.AUTO:
            # Auto-decide based on tool types and dependencies
            if len(tool_calls) == 1:
                return [await self.execute_tool(tool_calls[0])]

            # Analyze dependencies to decide execution mode
            independent, dependent = self._analyze_dependencies(tool_calls)

            if len(dependent) == 0:
                # All tools are independent, run in parallel
                return await self.execute_parallel(tool_calls)
            elif len(independent) == 0:
                # All tools are dependent, run sequentially
                return await self.execute_sequential(tool_calls)
            else:
                # Mixed dependencies, run hybrid approach
                return await self.execute_parallel(tool_calls)

        return []

    def parse_tool_calls_from_response(self, response_content: str) -> List[ToolCall]:
        """Parse tool calls from AI response content"""
        import re

        tool_calls = []

        # Pattern to match function calls in the response
        # Matches patterns like: tool_name("arg1", "arg2", key="value")
        pattern = r'(\w+)\s*\(\s*([^)]*)\s*\)'

        matches = re.finditer(pattern, response_content)

        for match in matches:
            tool_name = match.group(1)
            args_str = match.group(2)

            # Check if this is a valid tool
            if tool_name not in self.tools:
                continue

            # Parse arguments
            arguments = self._parse_function_arguments(args_str)

            tool_calls.append(ToolCall(
                name=tool_name,
                arguments=arguments,
                call_id=f"call_{len(tool_calls)}"
            ))

        return tool_calls

    def _parse_function_arguments(self, args_str: str) -> Dict[str, Any]:
        """Parse function arguments from string"""
        import re
        import ast

        arguments = {}

        if not args_str.strip():
            return arguments

        try:
            # Try to parse as Python literal
            # Handle simple cases like: "arg1", "arg2", key="value"

            # Split by commas, but respect quotes
            args_parts = []
            current_part = ""
            in_quotes = False
            quote_char = None

            for char in args_str:
                if char in ['"', "'"] and not in_quotes:
                    in_quotes = True
                    quote_char = char
                    current_part += char
                elif char == quote_char and in_quotes:
                    in_quotes = False
                    quote_char = None
                    current_part += char
                elif char == ',' and not in_quotes:
                    args_parts.append(current_part.strip())
                    current_part = ""
                else:
                    current_part += char

            if current_part.strip():
                args_parts.append(current_part.strip())

            # Parse each argument
            positional_count = 0
            for part in args_parts:
                part = part.strip()
                if not part:
                    continue

                if '=' in part:
                    # Keyword argument
                    key, value = part.split('=', 1)
                    key = key.strip()
                    value = value.strip()

                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]

                    arguments[key] = value
                else:
                    # Positional argument
                    value = part

                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]

                    # Map to parameter names based on tool
                    # This is a simplified mapping - in practice, you'd want to use
                    # the actual function signature
                    param_names = ["command", "pattern", "filepath", "query", "files"]
                    if positional_count < len(param_names):
                        arguments[param_names[positional_count]] = value

                    positional_count += 1

        except Exception:
            # Fallback: treat the entire string as a single argument
            arguments["command"] = args_str.strip().strip('"\'')

        return arguments

# Global tool executor instance
tool_executor = ToolExecutor()
