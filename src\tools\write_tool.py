"""
Write Tool - File creation and content writing
"""
import os
import shutil
from typing import Dict, Any, Optional, List
from pathlib import Path
import tempfile


class WriteTool:
    """File writing tool for creating and updating files"""
    
    def __init__(self, backup_enabled: bool = True):
        self.backup_enabled = backup_enabled
        self.backup_dir = Path.home() / ".arien-ai" / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    async def write_file(self, filepath: str, content: str, 
                        encoding: str = 'utf-8', create_dirs: bool = True,
                        backup: Optional[bool] = None) -> Dict[str, Any]:
        """
        Write content to a file
        
        Args:
            filepath: Path to the file to write
            content: Content to write to the file
            encoding: File encoding (default: utf-8)
            create_dirs: Create parent directories if they don't exist
            backup: Create backup of existing file (default: use global setting)
            
        Returns:
            Dict with write operation results
        """
        try:
            file_path = Path(filepath).resolve()

            # Track if we created directories
            created_dirs = False

            # Create parent directories if needed
            if create_dirs and not file_path.parent.exists():
                file_path.parent.mkdir(parents=True, exist_ok=True)
                created_dirs = True

            # Check if parent directory exists
            if not file_path.parent.exists():
                return {
                    "success": False,
                    "error": f"Parent directory does not exist: {file_path.parent}",
                    "filepath": str(file_path)
                }
            
            # Create backup if file exists
            backup_path = None
            if file_path.exists():
                should_backup = backup if backup is not None else self.backup_enabled
                if should_backup:
                    backup_path = await self._create_backup(file_path)
            
            # Write content to temporary file first
            temp_file = None
            try:
                with tempfile.NamedTemporaryFile(
                    mode='w', 
                    encoding=encoding, 
                    delete=False,
                    dir=file_path.parent
                ) as tf:
                    tf.write(content)
                    temp_file = tf.name
                
                # Atomic move to final location
                shutil.move(temp_file, str(file_path))
                
                # Get file info
                stat_info = file_path.stat()
                
                return {
                    "success": True,
                    "filepath": str(file_path),
                    "size": len(content),
                    "size_bytes": stat_info.st_size,
                    "encoding": encoding,
                    "backup_created": backup_path is not None,
                    "backup_path": backup_path,
                    "created_dirs": created_dirs,
                    "lines": content.count('\n') + 1 if content else 0
                }
                
            except Exception as e:
                # Clean up temp file on error
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except:
                        pass
                raise e
                
        except PermissionError:
            return {
                "success": False,
                "error": f"Permission denied: {filepath}",
                "filepath": filepath
            }
        except UnicodeEncodeError as e:
            return {
                "success": False,
                "error": f"Encoding error ({encoding}): {str(e)}",
                "filepath": filepath
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Write error: {str(e)}",
                "filepath": filepath
            }
    
    async def _create_backup(self, file_path: Path) -> Optional[str]:
        """Create backup of existing file"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.name}.{timestamp}.backup"
            backup_path = self.backup_dir / backup_name
            
            shutil.copy2(str(file_path), str(backup_path))
            return str(backup_path)
        except Exception:
            return None
    
    def list_backups(self, filepath: Optional[str] = None) -> List[Dict[str, Any]]:
        """List available backups"""
        backups = []
        try:
            for backup_file in self.backup_dir.glob("*.backup"):
                if filepath:
                    # Filter by original filename
                    original_name = backup_file.name.split('.')[0]
                    if original_name != Path(filepath).name:
                        continue
                
                stat_info = backup_file.stat()
                backups.append({
                    "backup_path": str(backup_file),
                    "original_name": backup_file.name.split('.')[0],
                    "timestamp": backup_file.name.split('.')[-2],
                    "size": stat_info.st_size,
                    "created": stat_info.st_mtime
                })
        except Exception:
            pass
        
        return sorted(backups, key=lambda x: x["created"], reverse=True)
    
    async def restore_backup(self, backup_path: str, target_path: str) -> Dict[str, Any]:
        """Restore file from backup"""
        try:
            backup_file = Path(backup_path)
            target_file = Path(target_path)
            
            if not backup_file.exists():
                return {
                    "success": False,
                    "error": f"Backup file not found: {backup_path}"
                }
            
            # Create backup of current file before restore
            current_backup = None
            if target_file.exists():
                current_backup = await self._create_backup(target_file)
            
            # Copy backup to target
            shutil.copy2(str(backup_file), str(target_file))
            
            return {
                "success": True,
                "restored_from": backup_path,
                "restored_to": target_path,
                "current_backup": current_backup
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Restore error: {str(e)}"
            }


# Tool function for AI agent
async def write(filepath: str, content: str, encoding: str = 'utf-8', 
                create_dirs: bool = True) -> Dict[str, Any]:
    """
    Create or overwrite files with content
    
    Usage Examples:
    - write("/tmp/script.py", "print('Hello World')")
    - write("config.yaml", yaml_content)
    - write("/etc/hosts", hosts_content)
    - write("data.json", json.dumps(data), encoding="utf-8")
    
    Args:
        filepath: Path where to write the file
        content: Content to write to the file
        encoding: File encoding (default: utf-8)
        create_dirs: Create parent directories if needed
    
    Returns:
        Dictionary with write operation results
    """
    tool = WriteTool()
    result = await tool.write_file(filepath, content, encoding, create_dirs)
    
    # Format output for AI agent
    if result["success"]:
        output = f"✅ File written successfully\n"
        output += f"Path: {result['filepath']}\n"
        output += f"Size: {result['size']} characters ({result['size_bytes']} bytes)\n"
        output += f"Lines: {result['lines']}\n"
        output += f"Encoding: {result['encoding']}\n"
        
        if result["backup_created"]:
            output += f"Backup created: {result['backup_path']}\n"
        
        if result.get("created_dirs"):
            output += f"Created parent directories\n"
    else:
        output = f"❌ Failed to write file\n"
        output += f"Path: {result['filepath']}\n"
        output += f"Error: {result['error']}\n"
    
    return {
        "tool": "write",
        "success": result["success"],
        "output": output,
        "raw_result": result
    }
